import 'package:flutter/material.dart';
import '../../domain/entities/food_entry.dart';
import '../../domain/entities/nutritional_goals.dart';
import '../../domain/usecases/get_daily_nutrition_usecase.dart';
import '../../domain/repositories/food_entry_repository.dart';
import '../../core/utils/date_utils.dart';

class NutritionProvider extends ChangeNotifier {
  final GetDailyNutritionUseCase _getDailyNutritionUseCase;
  final FoodEntryRepository _foodEntryRepository;

  NutritionProvider(
    this._getDailyNutritionUseCase,
    this._foodEntryRepository,
  );

  bool _isLoading = false;
  String? _error;
  DailyNutritionData? _currentDayData;
  DateTime _selectedDate = DateTime.now();
  List<FoodEntry> _recentEntries = [];

  bool get isLoading => _isLoading;
  String? get error => _error;
  DailyNutritionData? get currentDayData => _currentDayData;
  DateTime get selectedDate => _selectedDate;
  List<FoodEntry> get recentEntries => _recentEntries;

  NutritionalGoals? get nutritionalGoals => _currentDayData?.nutritionalGoals;
  Map<String, double> get progressPercentages => _currentDayData?.progressPercentages ?? {};
  Map<String, List<FoodEntry>> get mealGroups => _currentDayData?.mealGroups ?? {};

  Future<void> loadDailyNutrition(String userId, {DateTime? date}) async {
    _setLoading(true);
    _clearError();

    try {
      final targetDate = date ?? _selectedDate;
      final params = GetDailyNutritionParams(
        userId: userId,
        date: targetDate,
      );

      final result = await _getDailyNutritionUseCase.execute(params);
      
      if (result.isSuccess) {
        _currentDayData = result.successValue;
        _selectedDate = targetDate;
      } else {
        _setError(result.failureValue?.message ?? 'Failed to load nutrition data');
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadRecentEntries(String userId) async {
    try {
      final result = await _foodEntryRepository.getFoodEntriesByUser(userId);
      if (result.isSuccess) {
        _recentEntries = result.successValue!.take(10).toList();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading recent entries: $e');
    }
  }

  Future<void> addFoodEntry(FoodEntry entry) async {
    try {
      final result = await _foodEntryRepository.createFoodEntry(entry);
      if (result.isSuccess) {
        // Reload current day data if the entry is for the selected date
        if (AppDateUtils.isSameDay(entry.consumedAt, _selectedDate)) {
          await loadDailyNutrition(entry.userId, date: _selectedDate);
        }
        // Update recent entries
        _recentEntries.insert(0, entry);
        if (_recentEntries.length > 10) {
          _recentEntries = _recentEntries.take(10).toList();
        }
        notifyListeners();
      } else {
        _setError(result.failureValue?.message ?? 'Failed to add food entry');
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  Future<void> updateFoodEntry(FoodEntry entry) async {
    try {
      final result = await _foodEntryRepository.updateFoodEntry(entry);
      if (result.isSuccess) {
        // Reload current day data if the entry is for the selected date
        if (AppDateUtils.isSameDay(entry.consumedAt, _selectedDate)) {
          await loadDailyNutrition(entry.userId, date: _selectedDate);
        }
        // Update recent entries
        final index = _recentEntries.indexWhere((e) => e.id == entry.id);
        if (index != -1) {
          _recentEntries[index] = entry;
          notifyListeners();
        }
      } else {
        _setError(result.failureValue?.message ?? 'Failed to update food entry');
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  Future<void> deleteFoodEntry(String entryId, String userId) async {
    try {
      final result = await _foodEntryRepository.deleteFoodEntry(entryId);
      if (result.isSuccess) {
        // Reload current day data
        await loadDailyNutrition(userId, date: _selectedDate);
        // Remove from recent entries
        _recentEntries.removeWhere((entry) => entry.id == entryId);
        notifyListeners();
      } else {
        _setError(result.failureValue?.message ?? 'Failed to delete food entry');
      }
    } catch (e) {
      _setError('An unexpected error occurred: $e');
    }
  }

  void selectDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }
}
