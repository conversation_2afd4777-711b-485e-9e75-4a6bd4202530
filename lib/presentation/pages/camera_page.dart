import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/widgets/custom_button.dart';

class CameraPage extends StatefulWidget {
  final String mealType;

  const CameraPage({
    super.key,
    required this.mealType,
  });

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Add ${widget.mealType.toUpperCase()}'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.camera_alt,
                size: 100,
                color: Theme.of(context).colorScheme.primary,
              ),
              
              const SizedBox(height: AppConstants.largePadding),
              
              Text(
                'Camera Feature',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              Text(
                'Camera functionality will be implemented here.\nThis will include camera preview, photo capture, and image analysis.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              
              const SizedBox(height: AppConstants.largePadding),
              
              CustomButton(
                text: 'Take Photo',
                onPressed: () {
                  // TODO: Implement camera functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Camera feature coming soon!'),
                    ),
                  );
                },
                icon: Icons.camera_alt,
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              CustomButton(
                text: 'Choose from Gallery',
                onPressed: () {
                  // TODO: Implement gallery picker
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Gallery picker coming soon!'),
                    ),
                  );
                },
                isOutlined: true,
                icon: Icons.photo_library,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
